/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'
const AdminAffiliateCommissionController = () => import('#adminControllers/affiliation/affiliate_commission_controller')
const AdminAffiliationTierController = () => import('#adminControllers/affiliation/affiliation_tier_controller')
const AdminAffiliateController = () => import('#adminControllers/affiliation/affiliate_controller')

export default function adminAffiliationRoutes() {
  router
    .group(() => {
      router.get('/', [AdminAffiliationTierController, 'index'])
      router.post('/', [AdminAffiliationTierController, 'store'])
      router.get('/:id/next', [AdminAffiliationTierController, 'showNextTier'])

      router.get('/:id/commission-groups', [AdminAffiliationTierController, 'showCommissionGroups'])
      router.post('/:id/commission-groups', [AdminAffiliationTierController, 'storeCommissionGroup'])
      router.put('/:id/commission-groups/:groupId', [AdminAffiliationTierController, 'updateCommissionGroup'])
      router.delete('/:id/commission-groups/:groupId', [AdminAffiliationTierController, 'destroyCommissionGroup'])

      router.get('/:id/commission-groups/non-commission-products', [AdminAffiliationTierController, 'showNonCommissionProducts'])
      router.get('/:id/commission-groups/:groupId', [AdminAffiliationTierController, 'showProductsOfCommissionGroups'])
      router.post('/:id/commission-groups/:groupId', [AdminAffiliationTierController, 'updateProductsOfCommissionGroups'])

      router.get('/:id/discounted-products', [AdminAffiliationTierController, 'showDiscountedProducts'])
      router.get('/:id/full-priced-products', [AdminAffiliationTierController, 'getFullPricedProducts'])

      router.get('/:id', [AdminAffiliationTierController, 'show'])
      router.put('/:id', [AdminAffiliationTierController, 'update'])
      router.patch('/:id/commission-groups/:groupId/by-collection', [AdminAffiliationTierController, 'updateCommissionGroupByCollection'])
      router.patch('/:id/commission-groups/:groupId/by-products', [AdminAffiliationTierController, 'updateCommissionGroupByProductIds'])
      router.patch('/:id/discount-collection-by-collection', [AdminAffiliationTierController, 'updateDiscountCollectionByCollection'])
      router.patch('/:id/discount-collection-by-products', [AdminAffiliationTierController, 'updateDiscountCollectionByProductIds'])
      router.delete('/:id', [AdminAffiliationTierController, 'destroy'])
    })
    .prefix('affiliate-tiers')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.get('/', [AdminAffiliateController, 'index'])
      router.get('/:id', [AdminAffiliateController, 'show'])
      router.get('/:id/commission-stats', [AdminAffiliateController, 'stats'])
      router.get('/:id/payment-methods', [AdminAffiliateController, 'paymentMethods'])
      router.post('/:id/pay', [AdminAffiliateController, 'payForCommissions'])
      router.post('/', [AdminAffiliateController, 'store'])
      router.put('/:id', [AdminAffiliateController, 'update'])
      router.patch('/:id/preview-sync-data', [AdminAffiliateController, 'previewSyncData'])
      router.patch('/:id/proceed-sync-data', [AdminAffiliateController, 'proceedSyncData'])
      router.delete('/:id', [AdminAffiliateController, 'destroy'])
    })
    .prefix('affiliates')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

  router
    .group(() => {
      router.patch('/:id/sync-commission', [AdminAffiliateCommissionController, 'syncCommission'])
      router.patch('/sync-commissions', [AdminAffiliateCommissionController, 'syncAllCommissions'])

      router.get('/:id', [AdminAffiliateCommissionController, 'show'])
      router.put('/:id', [AdminAffiliateCommissionController, 'update'])
      router.patch('/:id', [AdminAffiliateCommissionController, 'setStatus'])
      router.get('/', [AdminAffiliateCommissionController, 'index'])
    })
    .prefix('commissions')
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))

}
