import type { HttpContext } from '@adonisjs/core/http'
import ZnSalonConstructionServiceSignup from '#models/zn_salon_construction_service_signup'
import {
  updateSalonConstructionServiceStatusValidator,
  salonConstructionServiceFilterValidator,
} from '#validators/salon_construction_service/salon_construction_service_validator'
import {
  SALON_CONSTRUCTION_SERVICE_TEXT,
  ESalonConstructionServiceStatus,
} from '../../../app/constants/salon_construction_service.js'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'

export default class AdminSalonConstructionServiceController {
  /**
   * @index
   * @tag Admin Salon Construction Service
   * @summary Get all salon construction service signups with filtering
   * @paramQuery page - Page number - @type(number) @example(1)
   * @paramQuery limit - Items per page - @type(number) @example(10)
   * @paramQuery status - Filter by status - @type(string) @example(pending)
   * @paramQuery search - Search by name, email, or business name - @type(string)
   * @paramQuery startDate - Filter from date - @type(date)
   * @paramQuery endDate - Filter to date - @type(date)
   * @paramQuery budgetRange - Filter by budget range - @type(string)
   * @responseBody 200 - {"success":true,"data":{"data":[<ZnSalonConstructionServiceSignup>],"meta":{}}} - Success
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const filters = await salonConstructionServiceFilterValidator.validate(request.qs())

      const page = filters.page || 1
      const limit = filters.limit || 10

      const query = ZnSalonConstructionServiceSignup.query()
        .preload('pdfFile')
        .orderBy('createdAt', 'desc')

      if (filters.status) {
        query.where('status', filters.status)
      }

      if (filters.search) {
        query.where((builder) => {
          builder
            .whereILike('fullName', `%${filters.search}%`)
            .orWhereILike('businessName', `%${filters.search}%`)
            .orWhereILike('emailAddress', `%${filters.search}%`)
        })
      }

      if (filters.startDate) {
        query.where('createdAt', '>=', filters.startDate)
      }

      if (filters.endDate) {
        query.where('createdAt', '<=', filters.endDate)
      }

      if (filters.budgetRange) {
        query.where('budgetRange', filters.budgetRange)
      }

      const signups = await query.paginate(page, limit)

      return response.ok({
        success: true,
        data: signups,
      })
    } catch (error) {
      console.error('Error fetching salon construction service signups:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch signups',
      })
    }
  }

  /**
   * @show
   * @tag Admin Salon Construction Service
   * @summary Get salon construction service signup by ID
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - {"success":true,"data":<ZnSalonConstructionServiceSignup>} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const signup = await ZnSalonConstructionServiceSignup.query()
        .where('id', params.id)
        .preload('pdfFile')
        .first()

      if (!signup) {
        return response.notFound({
          success: false,
          message: SALON_CONSTRUCTION_SERVICE_TEXT.NOT_FOUND,
        })
      }

      return response.ok({
        success: true,
        data: signup,
      })
    } catch (error) {
      console.error('Error fetching salon construction service signup:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch signup',
      })
    }
  }

  /**
   * @update
   * @tag Admin Salon Construction Service
   * @summary Update salon construction service signup status (approve/reject)
   * @paramPath id - Signup ID - @type(string) @required
   * @requestBody {"status":"approved|rejected","rejectionReason":"Optional reason for rejection"}
   * @responseBody 200 - {"success":true,"message":"Status updated successfully","data":<ZnSalonConstructionServiceSignup>} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   * @responseBody 400 - {"success":false,"message":"Validation failed"} - Validation Error
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const data = request.all()
      const payload = await updateSalonConstructionServiceStatusValidator.validate(data)

      const signup = await ZnSalonConstructionServiceSignup.findOrFail(params.id)

      signup.status = payload.status

      if (payload.status === ESalonConstructionServiceStatus.REJECTED && payload.rejectionReason) {
        signup.additionalNotes =
          `${signup.additionalNotes || ''}\n\nRejection Reason: ${payload.rejectionReason}`.trim()
      }

      await signup.save()

      const message =
        payload.status === ESalonConstructionServiceStatus.APPROVED
          ? SALON_CONSTRUCTION_SERVICE_TEXT.APPROVAL_SUCCESS
          : SALON_CONSTRUCTION_SERVICE_TEXT.REJECTION_SUCCESS

      return response.ok({
        success: true,
        message,
        data: signup,
      })
    } catch (error) {
      console.error('Error updating salon construction service signup:', error)

      if (error.messages) {
        return response.badRequest({
          success: false,
          message: SALON_CONSTRUCTION_SERVICE_TEXT.VALIDATION_ERROR,
          errors: error.messages,
        })
      }

      return response.internalServerError({
        success: false,
        message: 'Failed to update signup',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Salon Construction Service
   * @summary Soft delete salon construction service signup
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - {"success":true,"message":"Signup deleted successfully"} - Success
   * @responseBody 404 - {"success":false,"message":"Salon construction service signup not found"} - Not Found
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const signup = await ZnSalonConstructionServiceSignup.findOrFail(params.id)
      await signup.softDelete()

      return response.ok({
        success: true,
        message: 'Signup deleted successfully',
      })
    } catch (error) {
      console.error('Error deleting salon construction service signup:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to delete signup',
      })
    }
  }

  /**
   * @downloadPdf
   * @tag Admin Salon Construction Service
   * @summary Download PDF for salon construction service signup
   * @paramPath id - Signup ID - @type(string) @required
   * @responseBody 200 - PDF file - Success
   * @responseBody 404 - {"success":false,"message":"PDF not found"} - Not Found
   */
  async downloadPdf({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const signup = await ZnSalonConstructionServiceSignup.query()
        .where('id', params.id)
        .preload('pdfFile')
        .first()

      if (!signup || !signup.pdfFile) {
        return response.notFound({
          success: false,
          message: 'PDF not found',
        })
      }

      return response.redirect(signup.pdfFile.url)
    } catch (error) {
      console.error('Error downloading PDF:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to download PDF',
      })
    }
  }

  /**
   * @stats
   * @tag Admin Salon Construction Service
   * @summary Get salon construction service statistics
   * @responseBody 200 - {"success":true,"data":{"total":0,"pending":0,"approved":0,"rejected":0}} - Success
   */
  async stats({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.SALON_CONSTRUCTION_SERVICE)

    try {
      const [total, pending, approved, rejected] = await Promise.all([
        ZnSalonConstructionServiceSignup.query().count('* as total'),
        ZnSalonConstructionServiceSignup.query()
          .where('status', ESalonConstructionServiceStatus.PENDING)
          .count('* as total'),
        ZnSalonConstructionServiceSignup.query()
          .where('status', ESalonConstructionServiceStatus.APPROVED)
          .count('* as total'),
        ZnSalonConstructionServiceSignup.query()
          .where('status', ESalonConstructionServiceStatus.REJECTED)
          .count('* as total'),
      ])

      return response.ok({
        success: true,
        data: {
          total: total[0].$extras.total,
          pending: pending[0].$extras.total,
          approved: approved[0].$extras.total,
          rejected: rejected[0].$extras.total,
        },
      })
    } catch (error) {
      console.error('Error fetching salon construction service stats:', error)
      return response.internalServerError({
        success: false,
        message: 'Failed to fetch stats',
      })
    }
  }
}
