import { EApprovalStatus } from "#constants/approval_status"
import { ACTION, RESOURCE } from "#constants/authorization"
import ZnAffiliate from "#models/zn_affiliate"
import { AffiliationService } from "#services/affiliation/affiliation_service"
import { payCommisionValidator } from "#validators/affiliate"
import { HttpContext } from "@adonisjs/core/http"
import { isValid, parse } from "date-fns";

enum EAction {
  APPROVAL = 'approval',
  SET_REF_CODE = 'set-ref-code',
  UPDATE_TIER = 'update-tier'
}

export default class AdminAffiliateController {
  private affiliateService: AffiliationService;

  constructor() {
    this.affiliateService = new AffiliationService();
  }

  async index({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION);
      const { page = 1, limit = 10, advancedFilter } = request.qs();

      const query = ZnAffiliate.query()
        .preload('user')
        .preload('socialPages');

      if (advancedFilter && advancedFilter === 'approved') {
        query.where('registerStatus', EApprovalStatus.APPROVED);
      }

      query.whereHas('user', (query) => {
        query.whereNull('deletedAt');
      });

      const affiliates = await query.paginate(page, limit);

      return response.ok(affiliates);
    } catch (error) {
      return response.badRequest(error);
    }
  }

  async store({ bouncer, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION);
      const userId = request.input('userId');
      return await this.affiliateService.registerAffiliateForExistingUser(userId);
    } catch (error) {
      return response.badRequest(error);
    }
  }

  async show({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      return await this.affiliateService.getAffiliate(params.id);

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async stats({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)

      const affiliateId = params.id;
      const startDateParam = request.input('startDate');
      const endDateParam = request.input('endDate');

      const startDate = this.parseDate(startDateParam);
      const endDate = this.parseDate(endDateParam);

      const stats = await this.affiliateService.getStatistics(affiliateId, { from: startDate, to: endDate });

      return response.ok(stats);

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async paymentMethods({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.AFFILIATION)
      return await this.affiliateService.getPaymentMethods(params.id);

    } catch (error) {
      console.error(error);
      if (error.status == 500)
        return response.internalServerError(error);
      else
        return response.badRequest(error);
    }
  }

  async payForCommissions({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.AFFILIATION)

    try {
      const data = request.all()
      const payload = await payCommisionValidator.validate(data);

      const result = await this.affiliateService.payForCommissions(params.id, payload.amount, payload.paymentMethodId);

      if (result.success)
        return response.ok(result);
      else
        return response.abort(result);

    } catch (error) {
      return response.badRequest(error)
    }
  }

  async update({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.APPROVE || ACTION.REJECT || ACTION.UPDATE, RESOURCE.AFFILIATION)

      const affiliateId = params.id;
      const action = request.input('action');
      const data = request.all()

      switch (action) {

        case EAction.APPROVAL:
          return response.ok(await this.affiliateService.verifyRegistrationStatus(affiliateId, data));

        case EAction.SET_REF_CODE:
          return response.ok(await this.affiliateService.setRefCode(affiliateId, data.refCode))

        case EAction.UPDATE_TIER:
          return response.ok(await this.affiliateService.updateTier(affiliateId, data));

        default:
          console.error('No action specified');
          return response.badRequest('No action specified');
      }

    } catch (error) {
      console.error(error);
      return response.badRequest(error.message)
    }
  }

  async previewSyncData({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const affiliateId = params.id;
      return await this.affiliateService.previewSyncData(affiliateId)

    } catch (error) {
      return response.badRequest(error);
    }
  }

  async proceedSyncData({ bouncer, params, request, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.AFFILIATION)

      const affiliateId = params.id;
      const data = request.all()
      await this.affiliateService.proceedSyncData(affiliateId, data)

      return response.ok(`Affiliate ID ${affiliateId} has been sync amounts successfully.`)

    } catch (error) {
      return response.badRequest(error);
    }
  }

  async destroy({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.AFFILIATION)

      const affiliateId = params.id;
      this.affiliateService.delete(affiliateId);

      return response.ok(`Affiliate ID ${affiliateId} has been deleted successfully.`);
    } catch (error) {
      return response.badRequest(error);
    }
  }

  private parseDate(inputDate: string): Date | null {
    if (!inputDate) {
      return null;
    }

    const date = parse(inputDate, 'yyyy-MM-dd', new Date());
    return isValid(date) ? date : null;
  }
}