import SyncShopifyOrdersJob from '#jobs/sync_shopify_orders_job'
import { ShopifyService } from '#services/shopify/shopify_service'
import { args, BaseCommand } from '@adonisjs/core/ace'
import type { CommandOptions } from '@adonisjs/core/types/ace'
import queue from '@rlanz/bull-queue/services/main'
import { getShopifyOrderId } from '../services/commons.js'

export default class SyncShopifyOrderById extends BaseCommand {
  static commandName = 'shopify:sync-order-by-id'
  static description = ''

  static options: CommandOptions = {
    startApp: true,
  }

  // Defining an argument
  @args.string({ description: 'Shopify Id of the order. Number part only.' })
  public id?: string

  async run() {
    this.logger.info('Hello world from "SyncShopifyOrderById"')
    console.log(this.id)

    if (!this.id) {
      this.logger.error('Shopify Id is required')
      return
    }

    const shopifyOrderId = getShopifyOrderId(this.id)
    const shopifyService = new ShopifyService()
    const { orders } = await shopifyService.fetchOrdersWithIds([shopifyOrderId])
    console.log(JSON.stringify(orders, null, 2))

    await queue.dispatch(
      SyncShopifyOrdersJob,
      { orders },
      {
        queueName: 'syncData',
      }
    )
  }
}
