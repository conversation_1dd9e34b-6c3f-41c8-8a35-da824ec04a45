import AppMail from '#mails/app_mail';
import ZnVendor from '#models/zn_vendor';

export default class VendorRegistrationApprovedNotification extends AppMail {
  subject = "Welcome to Zurno - Your Vendor Account is Now Active! 🎉"

  constructor(
    private vendor: ZnVendor
  ) {
    super()
  }

  prepare() {
    this.message
      .htmlView('mails/vendors/vendor_registration_approved.edge', {
        serverDomain: this.baseUrl,
        vendor: this.vendor,
      })
      .to(this.vendor.email)
  }
}
