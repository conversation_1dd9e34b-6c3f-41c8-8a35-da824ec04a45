import AppMail from '#mails/app_mail';

export default class VendorRegistrationConfirmNotification extends AppMail {
  subject = "Welcome to Zurno - Your Application is Under Review"

  constructor(
    private companyName: string,
    private userEmail: string,
  ) {
    super()
  }

  prepare() {
    this.message
      .htmlView('mails/vendors/vendor_registration_confirm.edge', {
        serverDomain: this.baseUrl,
        companyName: this.companyName,
      })
      .to(this.userEmail)
  }
}
