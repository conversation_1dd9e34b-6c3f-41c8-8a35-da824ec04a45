import AppMail from '#mails/app_mail';

export default class VendorRegistrationRejectedNotification extends AppMail {
  subject = "Update on Your Vendor Application"

  constructor(
    private companyName: string,
    private userEmail: string,
    private rejectionReason: string,
  ) {
    super()
  }

  prepare() {
    this.message
      .htmlView('mails/vendors/vendor_registration_rejected.edge', {
        serverDomain: this.baseUrl,
        companyName: this.companyName,
        rejectionReason: this.rejectionReason,
      })
      .to(this.userEmail)
  }
}
