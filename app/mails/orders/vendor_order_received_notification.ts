import AppMail from '#mails/app_mail';
import ZnVendorOrder from '#models/zn_vendor_order';

export default class VendorOrderReceivedNotification extends AppMail {
  constructor(
    private vendorOrder: ZnVendorOrder,
  ) {
    super()
  }

  prepare() {
    const orderTimeString = this.vendorOrder.createdAt.toFormat('EEE, MMM dd, yyyy, hh:mm a');
    const customerName = `${this.vendorOrder.order.user?.firstName} ${this.vendorOrder.order.user?.lastName}`;

    this.message
      .subject(`New Order Received - Order ${this.vendorOrder.order.name}`)
      .htmlView('mails/orders/vendor_order', {
        serverDomain: this.baseUrl,
        companyName: this.vendorOrder.vendor.companyName,
        orderName: this.vendorOrder.order.name,
        orderDate: orderTimeString,
        orderTotal: this.vendorOrder.totalPrice,
        orderDetails: this.vendorOrder.orderDetails,
        customerName,
        shippingAddress: this.vendorOrder.order.shippingAddress.formatAddress,
      })
      .to(this.vendorOrder.vendor.email)
  }
}
