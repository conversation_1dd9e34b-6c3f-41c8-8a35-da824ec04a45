import ZnCancelReasons from '#models/zn_cancel_reasons'
import ZnCollection from '#models/zn_collection'
import ZnProduct from '#models/zn_product'
import ZnProductCategory from '#models/zn_product_category'
import ZnStoreSetting from '#models/zn_store_setting'
import ZnUser from '#models/zn_user'
import { ResourceFavoriteService } from '#services/resource_favorite_service'
import { ProductService } from '#services/shop/product_service'
import { StorefrontShopifyService } from '#services/shopify/storefront_shopify_service'
import env from '#start/env'
import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'

export default class ShopController {
  private storefrontShopify
  private resourceFavoriteService
  private productService

  constructor() {
    this.storefrontShopify = new StorefrontShopifyService()
    this.resourceFavoriteService = new ResourceFavoriteService()
    this.productService = new ProductService()
  }

  /**
   * @listCategories
   * @tag Shop
   * @summary Get List Categories
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async listCategories({ request, response }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const categoryIds = (
      await ZnStoreSetting.query()
        .where('sourceName', ZnProductCategory.table)
        .select('sourceId')
        .orderBy('orderBy', 'asc')
    ).map((setting) => setting.sourceId)

    const categories = await ZnProductCategory.query()
      .whereIn('id', categoryIds)
      .where('isArchived', false)
      .preload('image')
      .orderByRaw('FIELD(id, ?) asc', [categoryIds])
      .orderBy('name', 'asc')
      .paginate(page, limit)

    return response.ok(categories)
  }

  /**
   * @showCategory
   * @tag Shop
   * @summary Get Category Detail with SubCategories and Collections
   * @paramPath id - Category ID - @type(string) @required
   * @responseBody 200 - <ZnProductCategory>.with(image,subCategories,subCategories.image,subCategories.collections,collections,collections.image) - Get Category Detail with SubCategories and Collections descriptively
   */
  async showCategory({ params, response }: HttpContext) {
    try {
      const idOrGid = decodeURIComponent(params.id)
      let categoryQuery = ZnProductCategory.query().where('isArchived', false)

      if (idOrGid.startsWith('gid:')) {
        categoryQuery = categoryQuery.where('shopifyId', idOrGid)
      } else {
        categoryQuery = categoryQuery.where('id', idOrGid)
      }

      const category = await categoryQuery
        .preload('image')
        .preload('subCategories', (query) => {
          query
            .where('isArchived', false)
            .preload('image')
            .preload('collections', (collectionQuery) => {
              collectionQuery.where('status', true).select(['id', 'shopifyCollectionId', 'title'])
            })
            .withCount('collections', (collectionQuery) => {
              collectionQuery.where('status', true)
            })
        })
        .preload('collections', (query) => {
          query
            .where('status', true)
            .preload('image')
            .withCount('products', (productQuery) => {
              productQuery.whereNot('status', 'draft').where('isGift', false).has('variant')
            })
            .orderBy('orderBy', 'asc')
        })
        .firstOrFail()

      const productsCountPromises = category.subCategories.map(async (subCategory) => {
        const count = await db
          .from('zn_products')
          .where('categoryId', subCategory.id)
          .whereNot('status', 'draft')
          .where('isGift', false)
          .count('* as total')
          .first()

        return {
          categoryId: subCategory.id,
          count: Number(count?.total || 0),
        }
      })

      const productsCounts = await Promise.all(productsCountPromises)
      const serializedCategory = category.serialize()

      serializedCategory.subCategories = serializedCategory.subCategories.map(
        (subCategory: any) => ({
          ...subCategory,
          productsCount: productsCounts.find((p) => p.categoryId === subCategory.id)?.count || 0,
          shopifyCollectionIds:
            subCategory.collections?.map((collection: any) => collection.shopifyCollectionId) || [],
          collections: subCategory.collections || [],
        })
      )

      return response.ok(serializedCategory)
    } catch (error) {
      console.log(error)
      return response.internalServerError(error)
    }
  }

  /**
   * @listCategoryProducts
   * @tag Shop
   * @summary Get Category Products
   * @paramParam id - Category ID
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery sort - Sort by (default createdAt) - @type(string)
   * @paramQuery order - Sort order (default desc) - @type(string)
   */
  async listCategoryProducts({ params, request, response }: HttpContext) {
    const { page = 1, limit = 10, sort = 'updatedAt', order = 'desc' } = request.qs()

    const products = await ZnProduct.query()
      .where('categoryId', params.id)
      .whereNot('status', 'draft')
      .where('isGift', false)
      .has('variant')
      // Preload variants with inventory info
      .preload('variants', (query) => {
        query
          .select([
            'id',
            'productId',
            'title',
            'price',
            'compareAtPrice',
            'inventoryQuantity',
            'inventoryPolicy',
            'sku',
          ])
          .orderBy('price', 'asc')
      })
      .whereHas('channels', (channelQuery) => {
        channelQuery.where('isMobile', true).where('active', true)
      })
      // Preload product info
      .preload('image')
      .preload('productType')
      .preload('vendor')
      .preload('tags')
      .preload('reviewsSummary')
      // Get review stats
      .select('*')
      .select(
        db.raw(
          `
        (SELECT COUNT(*) FROM zn_product_reviews
         WHERE productId = zn_products.id
         AND status = 1) as review_count,
        (SELECT AVG(rating) FROM zn_product_reviews
         WHERE productId = zn_products.id
         AND status = 1) as average_rating
        `
        )
      )
      .orderBy(sort, order)
      .paginate(page, limit)

    return response.ok(products)
  }

  /**
   * @listCollections
   * @tag Shop
   * @summary Get List Collections
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @paramQuery categoryId - Category ID (optional) - @type(string)
   */
  async listCollections({ request, response }: HttpContext) {
    const { page = 1, limit = 10, categoryId } = request.qs()

    const collectionIds = (
      await ZnStoreSetting.query()
        .where('sourceName', ZnCollection.table)
        .select('sourceId')
        .orderBy('orderBy', 'asc')
    ).map((setting) => setting.sourceId)

    const query = ZnCollection.query()
      .whereIn('id', collectionIds)
      .where('status', true)
      .preload('image')
      .preload('categories')
      .withCount('products')
      .orderByRaw('FIELD(id, ?)', [collectionIds])
      .orderBy('updatedAt', 'desc')

    if (categoryId) {
      query.whereHas('categories', (categoryQuery) => {
        categoryQuery.where('id', categoryId)
      })
    }

    const collections = await query.paginate(page, limit)
    return response.ok(collections)
  }

  /**
   * @showCollection
   * @tag Shop
   * @summary Get Collection By Id
   * @paramParam id - Collection ID or Shopify GID
   * @paramQuery orderBy - Sort field (BEST_SELLING, TITLE, PRICE, CREATED)
   * @paramQuery reverse - Sort direction (true/false)
   */
  async showCollection({ params, request, response }: HttpContext) {
    const idOrGid = decodeURIComponent(params.id)
    let collectionQuery = ZnCollection.query().where('status', true)

    if (idOrGid.startsWith('gid:')) {
      collectionQuery = collectionQuery.where('shopifyCollectionId', idOrGid)
    } else {
      collectionQuery = collectionQuery.where('id', idOrGid)
    }

    const { orderBy = 'BEST_SELLING', reverse = false } = request.qs()
    let sortDirection: 'asc' | 'desc' = reverse === 'true' || reverse === true ? 'desc' : 'asc'

    const collection = await collectionQuery
      .preload('image')
      .preload('categories')
      .preload('products', (query) => {
        query
          .preload('variant')
          .preload('image')
          .preload('reviewsSummary')
          .whereNot('zn_products.status', 'draft')
          .where('zn_products.isGift', false)
          .whereNull('zn_products.deletedAt')
          .has('variant')
          .whereHas('channels', (channelQuery) => {
            channelQuery.where('isMobile', true).where('active', true)
          })

        if (orderBy === 'BEST_SELLING') {
          query.orderBy('zn_product_collections.orderBy', sortDirection)
        } else if (orderBy === 'TITLE') {
          query.orderBy('zn_products.title', sortDirection)
        } else if (orderBy === 'CREATED') {
          query.orderBy('zn_products.createdAt', sortDirection)
        } else if (orderBy === 'PRICE') {
          query.orderByRaw(
            `(SELECT price FROM zn_product_variants WHERE zn_product_variants.productId = zn_products.id AND zn_product_variants.deletedAt IS NULL ORDER BY position ASC LIMIT 1) ${sortDirection}`
          )
        }
      })
      .first()

    if (!collection) {
      return response.notFound('Collection not found')
    }

    return response.ok(collection)
  }

  /**
   * @mostPopularProducts
   * @tag Shop
   * @summary Get Most Popular Products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async mostPopularProducts({ request, response }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const popularProductsRaw = await db
      .from('zn_resource_interacts')
      .select('resourceId')
      .where('resource', 'ZnProduct')
      .orderByRaw(
        `
      (viewCount * 1 + likeCount * 3 + addToCartCount * 5) DESC
    `
      )
      .paginate(page, limit)

    const productIds = popularProductsRaw.all().map((row) => row.resourceId)

    if (!productIds.length) {
      return response.ok({
        meta: {
          total: 0,
          perPage: limit,
          currentPage: page,
          lastPage: 1,
        },
        data: [],
      })
    }

    const products = await ZnProduct.query()
      .whereIn('id', productIds)
      .whereNot('status', 'draft')
      .where('isGift', false)
      .whereHas('channels', (channelQuery) => {
        channelQuery.where('isMobile', true).where('active', true)
      })
      .preload('variants', (query) => {
        query.preload('image').preload('optionValues')
      })
      .preload('reviews', (query) => {
        query.limit(2).orderBy('createdAt', 'desc')
      })
      .preload('image')
      .preload('vendor')
      .preload('images')
      .preload('reviewsSummary')
      .preload('options', (query) => {
        query.preload('variantOptionValues').preload('productOptionValues')
      })
      .whereHas('channels', (channelQuery) => {
        channelQuery.where('isMobile', true).where('active', true)
      })


    const productMap = new Map(products.map((p) => [p.id, p]))
    const sortedProducts = productIds.map((id) => productMap.get(id)).filter(Boolean)

    return response.ok({
      meta: {
        total: popularProductsRaw.total,
        perPage: popularProductsRaw.perPage,
        currentPage: popularProductsRaw.currentPage,
        lastPage: popularProductsRaw.lastPage,
      },
      data: sortedProducts,
    })
  }

  /**
   * @forYouProducts
   * @tag Shop
   * @summary Get For You Products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async forYouProducts({ request, response, auth }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()
    let user = null

    try {
      // @ts-ignore
      user = auth.getUserOrFail().serialize() as ZnUser
    } catch (error) {
      // User not authenticated - continue with random recommendations
    }

    let randomProducts: any = []
    if (user) {
      // const userInteractions = await ZnTracking.query()
      //   .select('resourceId')
      //   .where('userId', user.id)
      //   .whereIn('action', [
      //     TRACKING_ACTION.VIEW_PRODUCT,
      //     TRACKING_ACTION.FAVORITE_PRODUCT,
      //     TRACKING_ACTION.ADD_WISHLIST,
      //   ])
      //   .orderBy('createdAt', 'desc')
      //   .limit(10)
      //
      // if (userInteractions.length) {
      //   const interactedProducts = await ZnProduct.query()
      //     .whereIn(
      //       'id',
      //       userInteractions.map((p) => p.resourceId)
      //     )
      //     .preload('category')
      //
      //   const categoryIds = [
      //     ...new Set(interactedProducts.filter((p) => p.category).map((p) => p.category.id)),
      //   ]
      //
      //   if (categoryIds.length) {
      //     const recommendedProducts = await ZnProduct.query()
      //       .whereIn('categoryId', categoryIds)
      //       .whereNotIn(
      //         'id',
      //         userInteractions.map((p) => p.resourceId)
      //       )
      //       .preload('variant', (query) => {
      //         query.select(['id', 'price', 'compareAtPrice'])
      //         query.orderBy('price', 'asc')
      //       })
      //       .preload('image')
      //       .select('*')
      //       .select(
      //         db.raw(
      //           `
      //         (SELECT COUNT(*) FROM zn_trackings
      //          WHERE resourceId = zn_products.id
      //          AND action = ${TRACKING_ACTION.RATING_PRODUCT}) as rating_count,
      //         (SELECT AVG(ratingReviewStar) FROM zn_trackings
      //          WHERE resourceId = zn_products.id
      //          AND action = ${TRACKING_ACTION.RATING_PRODUCT}) as average_rating
      //       `
      //         )
      //       )
      //       .whereNot('status', 'draft')
      //       .has('variant')
      //       .orderBy(db.raw('RAND()'))
      //       .paginate(page, limit)
      //
      //     return response.ok(recommendedProducts)
      //   }
      // }
      randomProducts = await this.resourceFavoriteService.getRecommendProducts(user, page, limit)
    }

    if (!randomProducts || randomProducts.length === 0) {
      randomProducts = await ZnProduct.query()
        .whereNot('status', 'draft')
        .where('isGift', false)
        .preload('variants', (query) => {
          query.preload('image').preload('optionValues')
        })
        .preload('reviews', (query) => {
          query.limit(2).orderBy('createdAt', 'desc')
        })
        .preload('image')
        .preload('vendor')
        .preload('images')
        .preload('reviewsSummary')
        .preload('options', (query) => {
          query.preload('variantOptionValues').preload('productOptionValues')
        })
        .whereHas('channels', (channelQuery) => {
          channelQuery.where('isMobile', true).where('active', true)
        })
        .orderByRaw('RAND()')
        .paginate(page, limit)
    }

    return response.ok(randomProducts)
  }

  /**
   * @flashSaleProducts
   * @tag Shop
   * @summary Get Flash Sale Products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async flashSaleProducts({ request, response }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const flashSaleTag = env.get('SHOP_FLASH_SALE_TAG', 'flashsale')
    const products = await this.productService.productsByCollectionHandle(flashSaleTag, limit, page)

    return response.ok(products)
  }

  /**
   * @newArrivalsProducts
   * @tag Shop
   * @summary Get New Arrivals Products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async newArrivalsProducts({ request, response }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const newArrivalsTag = env.get('SHOP_NEW_ARRIVALS_TAG', 'newarrivals')
    const products = await this.productService.productsByCollectionHandle(
      newArrivalsTag,
      limit,
      page
    )

    return response.ok(products)
  }

  /**
   * @bestSellersProducts
   * @tag Shop
   * @summary Get Best Sellers Products
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   */
  async bestSellersProducts({ request, response }: HttpContext) {
    const { page = 1, limit = 10 } = request.qs()

    const bestSellersTag = env.get('SHOP_BEST_SELLERS_TAG', 'bestsellers')

    const products = await this.productService.productsByCollectionHandle(
      bestSellersTag,
      limit,
      page
    )

    return response.ok(products)
  }

  /**
   * @getMenu
   * @tag Shop
   */
  async getMenu({ request, response }: HttpContext) {
    const { handle } = request.qs()
    const menu = await this.storefrontShopify.getMenu(handle)

    const shopifyIds = menu?.items
      ?.filter((item: any) => item.items.length > 0)
      ?.flatMap((item: any) => item.items?.map((i: any) => i.resourceId) || [])

    const collections = await ZnCollection.query()
      .whereIn('shopifyCollectionId', shopifyIds)
      .orderBy('createdAt')

    return response.ok(collections)
  }

  async getCancelReason({ response }: HttpContext) {
    const result = await ZnCancelReasons.query()

    return response.ok(result)
  }
}
