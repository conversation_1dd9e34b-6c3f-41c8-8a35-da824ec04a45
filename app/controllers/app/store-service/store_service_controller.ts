import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import ZnStoreService from '#models/store_service/zn_store_service'
import { createServiceValidator, updateServiceValidator } from '#validators/store-service/index'

@inject()
export default class StoreServiceController {
  /**
   * @swagger
   * /api/v1/app/store-service/services:
   *   get:
   *     tags:
   *       - Store Service
   *     summary: List services grouped by category
   *     parameters:
   *       - name: storeId
   *         in: query
   *         required: true
   *         schema:
   *           type: string
   *       - name: page
   *         in: query
   *         schema:
   *           type: number
   *       - name: limit
   *         in: query
   *         schema:
   *           type: number
   *       - name: categories
   *         in: query
   *         schema:
   *           type: array
   *           items:
   *             type: string
   *       - name: priceFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: priceTo
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationTo
   *         in: query
   *         schema:
   *           type: number
   */
  async index({ request, response }: HttpContext) {
    const {
      page = 1,
      limit = 10,
      search,
      storeId,
      categories,
      priceFrom,
      priceTo,
      durationFrom,
      durationTo,
    } = await request.qs()

    let query = ZnStoreService.query().preload('categories').preload('image').preload('store')
    if (storeId) {
      query.where('storeId', storeId)
    }

    if (search) {
      query.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
    }

    if (categories?.length) {
      query.whereHas('categories', (query) => {
        query.whereIn('id', categories!)
      })
    }

    if (priceFrom !== undefined) {
      query.where('price', '>=', priceFrom)
    }

    if (priceTo !== undefined) {
      query.where('price', '<=', priceTo)
    }

    if (durationFrom !== undefined) {
      query.where('duration', '>=', durationFrom)
    }

    if (durationTo !== undefined) {
      query.where('duration', '<=', durationTo)
    }

    const result = await query.paginate(page, limit)

    return response.ok(result.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/services:
   *   post:
   *     tags:
   *       - Store Service
   *     summary: Create a new service
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - price
   *               - duration
   *               - storeId
   *               - categories
   *             properties:
   *               name:
   *                 type: string
   *               price:
   *                 type: number
   *               duration:
   *                 type: number
   *               storeId:
   *                 type: string
   *               imageId:
   *                 type: string
   *               categories:
   *                 type: array
   *                 items:
   *                   type: string
   */
  async store({ request, response }: HttpContext) {
    const payload = await createServiceValidator.validate(request.all())
    const service = await ZnStoreService.create(payload)

    if (payload.categories?.length && payload.categories.length > 0) {
      await service.related('categories').attach(payload.categories)
    }

    const loadPromises = [service.load('categories'), service.load('store')]

    // Only load image if imageId is not null
    if (service.imageId) {
      loadPromises.push(service.load('image'))
    }

    await Promise.all(loadPromises)

    return response.ok(service.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/services/{id}:
   *   put:
   *     tags:
   *       - Store Service
   *     summary: Update a service
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *               price:
   *                 type: number
   *               duration:
   *                 type: number
   *               imageId:
   *                 type: string
   *               categories:
   *                 type: array
   *                 items:
   *                   type: string
   */
  async update({ request, response, params }: HttpContext) {
    const payload = await updateServiceValidator.validate(request.all())
    const service = await ZnStoreService.findOrFail(params.id)

    await service.merge(payload).save()

    if (payload.categories?.length && payload.categories.length > 0) {
      await service.related('categories').sync(payload.categories)
    }

    const loadPromises = [service.load('categories'), service.load('store')]

    // Only load image if imageId is not null
    if (service.imageId) {
      loadPromises.push(service.load('image'))
    }

    await Promise.all(loadPromises)

    return response.ok(service.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/services/{id}:
   *   delete:
   *     tags:
   *       - Store Service
   *     summary: Delete a service
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   */
  async destroy({ params, response }: HttpContext) {
    const service = await ZnStoreService.findOrFail(params.id)
    await service.softDelete()
    return response.ok({
      message: 'Service deleted successfully',
    })
  }
}
