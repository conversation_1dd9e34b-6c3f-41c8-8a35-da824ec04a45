import env from '#start/env'
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg'
import ffprobeInstaller from '@ffprobe-installer/ffprobe'
import { randomUUID } from 'crypto'
import ffmpeg from 'fluent-ffmpeg'
import {createWriteStream, existsSync, mkdirSync, readFileSync, unlink } from 'fs'
import { PassThrough, Writable } from 'stream'
import {join, resolve} from "node:path";
import {tmpdir} from "node:os";
import {unlinkSync, writeFileSync} from "node:fs";
import axios from "axios";

export class VideoService {

  constructor() {
  }

  async getVideoMetadata(data: { url: string }) {
    return new Promise((resolve, reject) => {
      const ffprobePath = env.get('FFPROBE_PATH') || ffprobeInstaller.path

      ffmpeg.setFfprobePath(ffprobePath)

      ffmpeg(data.url)
        .ffprobe(0, (err, metadata) => {
          if (err) {
            reject(err)
          } else {
            resolve(metadata)
          }
        })

    })
  }

  async getVideoScreenShotBuffer({
                                   url,
                                   seek = 5,
                                 }: {
    url: string,
    seek?: number | 'random',
  }) {
    let innerSeek: any = 5

    let screenshotResolution = ''
    let width = 0
    let height = 0

    // // width / height
    // const croppedAspectRatio = 4 / 5
    // let croppedWidth = 0
    // let croppedHeight = 0
    // let croppedPosition = ''

    const bgAspectRatio = 16 / 9
    let croppedBgWidth = 0
    let croppedBgHeight = 0
    let croppedBgPosition = ''
    let bgWidth = 0
    let bgHeight = 0
    let bgPosition = ''

    let overlaySize = 0
    let overlayPosition = ''

    await this.getVideoMetadata({url})
      .then((metadata: any) => {
        const videoData = metadata.streams?.find((stream: any) => stream.codec_type == 'video')
        if (!videoData) {
          throw Error('Cannot get video metadata')
        }

        const videoFormat = metadata.format

        console.log('Received video metadata...')

        let randomSeek = 1
        let duration = videoData.duration
        // amazon stream file m3u8 does not have duration in stream metadata
        if (duration == 'N/A' || videoFormat?.format_name == 'hls') {
          duration = videoFormat.duration
        }
        // in case format does not have duration either
        if (!duration || duration == 'N/A') {
          duration = innerSeek
        }

        if (seek == 'random') {
          innerSeek = Math.floor(duration)
          randomSeek = Math.random()
        } else if (videoData.duration < seek) {
          innerSeek = Math.floor(duration)
        }

        innerSeek = (innerSeek * randomSeek).toFixed(1)

        width = videoData.width
        height = videoData.height

        if (width && height) {
          // scale down screenshot to max 500px width or height
          const maxWidthHeight = 500
          const aspectRatio = width / height
          if (width > height) {
            width = maxWidthHeight
            height = Math.ceil(width / aspectRatio)
          } else {
            height = maxWidthHeight
            width = Math.ceil(height * aspectRatio)
          }

          screenshotResolution = `${width}x${height}`

          // add background if video is vertical
          if (width < height) {
            // scale up bg
            bgHeight = height
            bgWidth = Math.ceil(height / aspectRatio)

            // crop screenshot for background
            croppedBgWidth = width
            croppedBgHeight = Math.ceil(croppedBgWidth / bgAspectRatio)
            croppedBgPosition = `${Math.ceil((bgWidth - croppedBgWidth) / 2)}:${Math.ceil((bgHeight - croppedBgHeight) / 2)}`

            // calculate position for screenshot overlay on bg
            bgPosition = `${Math.ceil((bgWidth - width) / 2)}:${Math.ceil((bgHeight - height) / 2) + 1}`
          }

          // // crop screenshot to fit 4:5 facebook ratio
          // if (width < height) {
          //   croppedWidth = width
          //   croppedHeight = Math.ceil(croppedWidth / croppedAspectRatio)
          // } else {
          //   croppedHeight = height
          //   croppedWidth = Math.ceil(croppedHeight * croppedAspectRatio)
          // }
          // croppedPosition = `${Math.ceil((width - croppedWidth) / 2)}:${Math.ceil((height - croppedHeight) / 2)}`


          // calculate play icon overlay size and position
          // overlaySize = Math.ceil(Math.min(croppedWidth, croppedHeight) / 5)
          // overlayPosition = `${Math.ceil((croppedWidth - overlaySize) / 2)}:${Math.ceil((croppedHeight - overlaySize) / 2)}`
          overlaySize = Math.ceil(Math.min(width, height) / 5)
          overlayPosition = `${Math.ceil((width - overlaySize) / 2)}:${Math.ceil((height - overlaySize) / 2)}`
        }
      })
      .catch((error) => {
        console.log(error);
        throw Error(error)
      })

    if (!screenshotResolution) {
      throw Error('Cannot get video resolution')
    }

    const ffmpegPath = env.get('FFMPEG_PATH') || ffmpegInstaller.path

    ffmpeg.setFfmpegPath(ffmpegPath)

    let screenshotStream = new PassThrough()

    const filepath = `public/thumbnails/${randomUUID()}.png`
    const result = await new Promise((resolve) => {
      // generate screenshot stream
      ffmpeg(url)
        .on('error', (error) => {
          console.log('screenshot error', error);
          resolve(null)
        })
        .on('start', () => {
          console.log('Start screenshot creating...');
        })
        .on('end', () => {
          console.log('End screenshot created...');
        })
        .seek(innerSeek)
        .takeFrames(1)
        .size(screenshotResolution)
        .outputOptions([
          '-vframes 1',
          '-f image2pipe',
          '-vcodec png'
        ])
        .pipe(screenshotStream)

      const complexFilters = [
        // `[0:v]crop=${croppedWidth}:${croppedHeight}:${croppedPosition}[resizedScreenshot]`,
        `[1:v]scale=${overlaySize}:${overlaySize}[resizedOverlay]`,
        // `[resizedScreenshot][resizedOverlay]overlay=${overlayPosition}`,
      ]

      if (croppedBgPosition) {
        complexFilters.push(
          `[0:v][resizedOverlay]overlay=${overlayPosition}[mainScreenshot]`,
          `[0:v]crop=${croppedBgWidth}:${croppedBgHeight}:${croppedBgPosition},scale=${bgWidth}:${bgHeight},gblur=sigma=20[background]`,
          `[background][mainScreenshot]overlay=${bgPosition}`,
        )
      } else {
        complexFilters.push(
          `[0:v][resizedOverlay]overlay=${overlayPosition}`,
        )
      }

      // overlay icon over screenshot, then i/o create the thumbnail file
      // return the thumbnail stream, then delete the file
      ffmpeg(screenshotStream)
        .on('error', (error) => {
          console.log('overlay error', error);
          resolve(null)
        })
        .on('start', () => {
          console.log('Start thumbnail creating...');
        })
        .on('end', () => {
          if (!existsSync(filepath)) {
            resolve(null)
          } else {
            console.log('Thumbnail', filepath, 'created');
            const file = readFileSync(filepath)
            resolve(file)
          }
        })
        .input('public/images/play-icon.png')
        .complexFilter(complexFilters)
        .saveToFile(filepath)
        .run()
    })

    if (existsSync(filepath)) {
      unlink(filepath, (err) => {
        if (err) {
          throw err
        } else {
          console.log('Thumbnail', filepath, 'deleted');
        }
      })
    }

    return result
  }

  async getTranscodedVideoBuffer({url}: { url: string }) {
    const ffmpegPath = env.get('FFMPEG_PATH') || ffmpegInstaller.path
    ffmpeg.setFfmpegPath(ffmpegPath)

    return await new Promise((resolve, reject) => {
      const buffers: any[] = [];
      const stream = new PassThrough();

      // Collect the data chunks
      const writable = new Writable({
        write(chunk, encoding, callback) {
          buffers.push(chunk);
          encoding
          callback();
        }
      });

      // Pipe ffmpeg output to the writable stream
      stream.pipe(writable);

      // Configure ffmpeg
      ffmpeg(url)
        .on('error', reject)
        .on('start', () => {
          console.log('Start transcoding video...');
        })
        .on('end', () => {
          console.log('Finish transcoding video...');

          resolve(Buffer.concat(buffers));
        })
        .videoCodec('libx264')
        .outputOptions([
          '-movflags frag_keyframe+empty_moov',
          '-preset slow',
          '-crf 23',
        ])
        .outputFormat('mp4')
        .pipe(stream, {end: true}); // pipe to PassThrough stream
    })
      .then((buffers: any) => {
        console.log('Buffer length:', buffers.length);

        return buffers
      })
      .catch(err => {
        console.error('FFmpeg error:', err);
        return null
      });
  }

  async getTranscodedVideoWithOuttro({
     url,
   }: {
    url: string;
  }) {
    const ffmpegPath = env.get('FFMPEG_PATH') || ffmpegInstaller.path
    ffmpeg.setFfmpegPath(ffmpegPath)

    // Ensure ./data folder exists
    const dataDir = resolve('./data')
    if (!existsSync(dataDir)) {
      mkdirSync(dataDir)
    }

    // Prepare file paths
    const outroPath = resolve('./data/zurno_outtro.mp4')
    const input1Path = resolve(await this.downloadToTempFile(url))
    const inputListPath = resolve(join(dataDir, `concat-list-${Date.now()}.txt`))

    // Write list file
    writeFileSync(
      inputListPath,
      `file '${input1Path.replace(/'/g, "'\\''")}'\nfile '${outroPath.replace(/'/g, "'\\''")}'`
    )

    // Transcode & concatenate
    return await new Promise((resolveFn, reject) => {
      const buffers: any[] = []
      const stream = new PassThrough()

      const writable = new Writable({
        write(chunk, _encoding, callback) {
          buffers.push(chunk)
          callback()
        },
      })

      stream.pipe(writable)

      ffmpeg()
        .input(inputListPath)
        .inputOptions(['-f', 'concat', '-safe', '0'])
        .videoCodec('libx264')
        .audioCodec('aac')
        .outputOptions([
          '-movflags', 'frag_keyframe+empty_moov',
          '-preset', 'slow',
          '-crf', '23',
        ])
        .outputFormat('mp4')
        .on('start', () => {
          console.log('Start transcoding and appending outro...')
        })
        .on('end', () => {
          console.log('Finish transcoding...')
          resolveFn(Buffer.concat(buffers))
        })
        .on('error', (err) => {
          console.error('FFmpeg error:', err)
          reject(err)
        })
        .pipe(stream, { end: true })
    })
      .then((buffer: any) => {
        console.log('Buffer length:', buffer.length)
        return buffer
      })
      .catch((err) => {
        console.error('[error] Cannot transcode video:', err)
        return null
      })
      .finally(() => {
        try {
          // unlinkSync(input1Path)
          // unlinkSync(inputListPath)
        } catch (e) {
          // ignore cleanup errors
        }
      })
  }

// Helper: Download remote video to temp file
  async downloadToTempFile(url: string): Promise<string> {
    const tmpPath = this.getTempFilePath('.mp4')
    const response = await axios.get(url, { responseType: 'stream' })
    const writer = createWriteStream(tmpPath)

    await new Promise((resolve, reject) => {
      response.data.pipe(writer)
      // @ts-ignore
      writer.on('finish', resolve)
      writer.on('error', reject)
    })

    return tmpPath
  }

// Helper: Generate local temp file path
  getTempFilePath(extension = '.mp4') {
    const filename = `video-${randomUUID()}${extension}`
    return resolve('./data', filename)
  }
}
