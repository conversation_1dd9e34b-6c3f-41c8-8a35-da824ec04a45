import { ACTION, RESOURCE } from '#constants/authorization'
import OrderRefundNotification from '#mails/orders/order_refund_notification'
import OrderOutOfStockNotification from '#mails/shop/order_out_of_stock_notification'
import ZnCoupon from '#models/zn_coupon'
import ZnOrder from '#models/zn_order'
import ZnProductVariant from '#models/zn_product_variant'
import { EDiscountValueType } from '#services/shopify/order/draft_order'
import logger from '@adonisjs/core/services/logger'
import mail from '@adonisjs/mail/services/main'
import { AdminNotificationService } from '../../../admin/services/notification/admin_notification_service.js'
import { ShopifyNotificationService } from '#services/shopify/webhooks/shopify_notification_service'
import ZnOrderFulfillment from '#models/zn_order_fulfillment'
import { v4 as uuidv4 } from 'uuid'
import ZnOrderDetail from "#models/zn_order_detail";
import db from "@adonisjs/lucid/services/db";
import ZnVendorOrder, { EVendorOrderStatus } from "#models/zn_vendor_order";
import VendorNotificationService from '#services/vendors/vendor_notification_service'

export class OrderService {
  private notificationService: ShopifyNotificationService;
  private vendorNotificationService: VendorNotificationService;

  constructor() {
    this.notificationService = new ShopifyNotificationService();
    this.vendorNotificationService = new VendorNotificationService();
  }

  async checkOrderOutOfStock(order: any, isDraft?: boolean) {
    const outOfStock = []
    for (const item of order?.line_items || []) {
      const variant = await ZnProductVariant.query()
        .where('shopifyVariantId', `gid://shopify/ProductVariant/${item.variant_id}`)
        .whereNot('sku', 'NVDPROTECTION%')
        .whereHas('product', (productQuery) => {
          productQuery.where('status', 'active').whereNot('title', 'Zurno Shipping Protection')
        })
        .preload('product')
        .first()

      if (variant && variant?.inventoryQuantity <= 0) {
        outOfStock.push(variant)
      }
    }

    if (outOfStock.length > 0) {
      // @ts-ignore
      await mail.sendLater(new OrderOutOfStockNotification(!!isDraft, order.name, outOfStock))
    }
  }

  async updateCouponOnOrderCreated(order: any) {
    const updatingOrder = await ZnOrder.query()
      .where('shopifyId', order.admin_graphql_api_id)
      .preload('orderDiscounts')
      .first()
    if (updatingOrder) {
      const discountCodes = updatingOrder.orderDiscounts.map((discount) => discount.discountCode)
      if (discountCodes && discountCodes.length > 0) {
        await ZnCoupon.query().whereIn('code', discountCodes).update({ deletedAt: new Date() })
      }
    }
  }

  async notifyAdminOnOrderRefunding(order: any) {
    const isZBACodeFound = order.discount_applications.some(
      (discount: any) =>
        discount.code !== null &&
        discount.code !== undefined &&
        typeof discount.code === 'string' &&
        discount.code.startsWith('ZBA')
    )
    if (!isZBACodeFound) return

    const orderName = order.name
    const orderUrl = order.order_status_url
    const discountCodes = order.discount_applications.map((item: any) => item.code).join(', ')

    const latestRefund = order.refunds.reduce((latest: any, current: any) => {
      return new Date(current.created_at) > new Date(latest.created_at) ? current : latest
    })

    const refundLineItems: any[] = []
    for (const refundLineItem of latestRefund.refund_line_items) {
      const name = refundLineItem.line_item.name
      const price = refundLineItem.line_item.price
      const quantity = refundLineItem.quantity
      const subTotal = refundLineItem.subtotal
      refundLineItems.push({
        name: name,
        price: price,
        quantity: quantity,
        subTotal: subTotal,
      })
    }

    // const admins = await ZnAdmin.all()
    const adminNotificationService = new AdminNotificationService()
    const admins = await adminNotificationService.getAdminsByPermissions([
      { action: ACTION.READ, resource: RESOURCE.ORDER }
    ])

    admins.forEach(async (admin) => {
      await mail
        .send(
          new OrderRefundNotification(
            admin.name ?? 'Admin',
            admin.username,
            orderName,
            orderUrl,
            discountCodes,
            refundLineItems
          )
        )
        .then(() => {
          logger.info(`Order refunded email has been sent successfully to ${admin.username}`)
        })
        .catch((error) => {
          logger.error('Error when sending email', error)
        })
    })
  }

  async getVariantsFromShopifyDraftOrder(draftOrder: any) {
    const zurnoLineItems = []
    for (const lineItem of draftOrder.lineItems.nodes) {
      const variant = await ZnProductVariant.query()
        .where('shopifyVariantId', lineItem.variant.id)
        .preload('image')
        .preload('product', (productQuery) => {
          productQuery.preload('image')
        })
        .preload('optionValues')
        .first()

      if (!variant) {
        continue
      }

      // Parse applied discount
      let discount
      if (lineItem.appliedDiscount) {
        discount = {
          id: null,
          title: lineItem.appliedDiscount.title,
          value: Number(lineItem.appliedDiscount.value),
          type:
            lineItem.appliedDiscount.valueType === EDiscountValueType.PERCENTAGE ? 'PER' : 'FIX',
        }
      }
      const originalTotal = Number(lineItem.originalTotalSet?.shopMoney?.amount || 0)
      const totalDiscount = Number(lineItem.totalDiscountSet?.shopMoney?.amount || 0)
      const giftId = lineItem.customAttributes.find((attr: any) => attr.key === 'giftId')?.value
      zurnoLineItems.push({
        id: lineItem.id,
        uuid: lineItem.uuid,
        availableForSale: variant.availableForSale,
        discount,
        optionValues: variant.optionValues,
        image: variant.image?.src || variant.product?.image?.src,
        productId: variant.product?.id,
        productName: variant.product?.title,
        variantId: variant.id,
        variantName: variant.title,
        quantity: lineItem.quantity,
        rawPrice: originalTotal,
        price: originalTotal - totalDiscount,
        sku: variant.sku,
        shopifyVariantId: variant.shopifyVariantId,
        shopifyProductId: variant.product?.shopifyProductId,
        isGift: Boolean(giftId),
        giftId,
      })
    }

    return zurnoLineItems
  }

  async createOrderFulfillment(payload: any, topic: string) {
    const fulfillmentId = payload.admin_graphql_api_id;
    if (!fulfillmentId) return;

    const fulfillmentStatus = payload.status;
    const shopifyOrderId = payload.order_id;
    const trackingCompany = payload.tracking_company;
    const trackingNumber = payload.tracking_number;
    const trackingNumbers = payload.tracking_numbers.join(',');;
    const trackingUrl = payload.tracking_url;
    const trackingUrls = payload.tracking_urls.join(',');;

    const order = await ZnOrder.query()
      .where('shopifyId', `gid://shopify/Order/${shopifyOrderId}`)
      .first();
    if (!order) return;

    const fulfillment = await ZnOrderFulfillment.updateOrCreate(
      { shopifyFulfillmentId: fulfillmentId, },
      {
        status: fulfillmentStatus,
        trackingCompany,
        trackingNumber,
        trackingNumbers,
        trackingUrl,
        trackingUrls,
        shopifyFulfillmentId: fulfillmentId,
        orderId: order.id,
      });

    for (const lineItem of payload.line_items) {
      const orderDetail = await order.related('orderDetails')
        .query()
        .where('shopifyId', lineItem.admin_graphql_api_id)
        .first();

      if (orderDetail) {
        orderDetail.fulfillmentStatus = lineItem.fulfillment_status || 'unfulfilled';
        await orderDetail.save();

        await orderDetail.related('fulfillments').attach({
          [fulfillment.id]: { id: uuidv4(), quantity: lineItem.quantity }
        });
      }
    }

    await this.updateFormerTrackingInfo(order, trackingCompany, trackingNumber, trackingUrl);
    await this.updateOrderFulfillmentStatus(order);

    await this.notificationService.fulfilNotification(order, fulfillmentStatus, topic);
  }

  async updateOrderFulfillment(payload: any, topic: string) {
    const fulfillmentId = payload.admin_graphql_api_id;
    if (!fulfillmentId) return;

    const fulfillmentStatus = payload.status;
    const shopifyOrderId = payload.order_id;
    const trackingCompany = payload.tracking_company;
    const trackingNumber = payload.tracking_number;
    const trackingNumbers = payload.tracking_numbers.join(',');
    const trackingUrl = payload.tracking_url;
    const trackingUrls = payload.tracking_urls.join(',');

    const order = await ZnOrder.query()
      .where('shopifyId', `gid://shopify/Order/${shopifyOrderId}`)
      .first();
    if (!order) return;

    const fulfillment = await ZnOrderFulfillment.findBy('shopifyFulfillmentId', fulfillmentId);
    if (!fulfillment) return;

    fulfillment.status = fulfillmentStatus;
    fulfillment.trackingCompany = trackingCompany;
    fulfillment.trackingNumber = trackingNumber;
    fulfillment.trackingNumbers = trackingNumbers;
    fulfillment.trackingUrl = trackingUrl;
    fulfillment.trackingUrls = trackingUrls;
    await fulfillment.save();

    for (const lineItem of payload.line_items) {
      const orderDetail = await order.related('orderDetails')
        .query()
        .where('shopifyId', lineItem.admin_graphql_api_id)
        .first();

      if (orderDetail) {
        orderDetail.fulfillmentStatus = lineItem.fulfillment_status || 'unfulfilled';
        await orderDetail.save();

        await orderDetail.related('fulfillments').sync({
          [fulfillment.id]: { id: uuidv4(), quantity: lineItem.quantity }
        });
      }
    }

    await this.updateOrderFulfillmentStatus(order);

    await this.notificationService.fulfilNotification(order, fulfillmentStatus, topic);
  }

  async updateFormerTrackingInfo(order: ZnOrder, trackingCompany: string, trackingNumber: string, trackingUrl: string) {
    order.trackingCompany = trackingCompany;
    order.trackingNumber = trackingNumber;
    order.trackingUrl = trackingUrl;
    await order.save();
  }

  async updateOrderFulfillmentStatus(order: ZnOrder) {
    await order.load('orderDetails');

    let areAllOrderDetailsFulfilled = true;
    let isAnyOrderDetailFulfilled = false;
    for (const orderDetail of order.orderDetails) {
      if (orderDetail.fulfillmentStatus !== 'fulfilled') {
        areAllOrderDetailsFulfilled = false;
      }
      if (orderDetail.fulfillmentStatus === 'fulfilled') {
        isAnyOrderDetailFulfilled = true;
      }
    }
    if (areAllOrderDetailsFulfilled) {
      order.fulfillmentStatus = 'fulfilled';
    } else if (isAnyOrderDetailFulfilled) {
      order.fulfillmentStatus = 'partially_fulfilled';
    }
    else {
      order.fulfillmentStatus = 'unfulfilled';
    }
    await order.save();
  }

  async separateVendorOrder(order: ZnOrder) {
    const dbOrder = await ZnOrder.query()
      .where('id', order.id)
      .preload('orderDetails', (query) => {
        query.preload('variant', (variantQuery) => {
          variantQuery.preload('product')
        })
      })
      .first()

    if (!dbOrder) {
      throw new Error(`Missing order`)
    }

    let isOrderCancelled
    if (dbOrder.status == 'cancel') {
      isOrderCancelled = true
    }

    const vendorMap: Record<string, ZnOrderDetail[]> = {}

    for (const detail of dbOrder.orderDetails) {
      const product = detail.variant.product
      const vendorId = product.vendorId

      if (vendorId) {
        if (!vendorMap[vendorId]) {
          vendorMap[vendorId] = []
        }
        vendorMap[vendorId].push(detail)
      }
    }

    const totalSubtotal = dbOrder.orderDetails.reduce(
      (sum, d) => sum + ((d.price ?? 0) * (d.quantity ?? 0)),
      0
    )

    const trx = await db.transaction()

    try {
      const newVendorOrderIds = []

      for (const [vendorId, details] of Object.entries(vendorMap)) {
        const subtotal = details.reduce((sum, d) => sum + ((d.price ?? 0) * (d.quantity ?? 0)), 0)
        const tax = details.reduce((sum, d) => sum + (parseFloat(d.tax?.toString() || '0') || 0), 0)
        const discount = details.reduce((sum, d) => sum + (d.discount ?? 0), 0)

        // Pro-rate shipping based on subtotal share
        const shipping = totalSubtotal > 0
          ? (subtotal / totalSubtotal) * (dbOrder.totalShipping ?? 0)
          : 0

        const total = subtotal + tax + shipping - discount

        const existingOrder = await ZnVendorOrder
          .query({ client: trx })
          .where('vendorId', vendorId)
          .andWhere('orderId', order.id)
          .first()

        if (!existingOrder) {
          // Create vendor order
          const vendorOrder = await ZnVendorOrder.create({
            orderId: dbOrder.id,
            vendorId,
            status: EVendorOrderStatus.PENDING,
            subtotalPrice: subtotal,
            totalTax: tax,
            totalDiscounts: discount,
            totalShipping: shipping,
            totalPrice: total,
            currentTotalPrice: total,
          }, { client: trx })

          for (const detail of details) {
            detail.vendorOrderId = vendorOrder.id
            await detail.useTransaction(trx).save()
          }

          newVendorOrderIds.push(vendorOrder.id)
        } else {
          // Update vendor order
          existingOrder.merge({
            subtotalPrice: subtotal,
            totalTax: tax,
            totalDiscounts: discount,
            totalShipping: shipping,
            totalPrice: total,
            currentTotalPrice: total,

            status: isOrderCancelled ? EVendorOrderStatus.CANCELLED : existingOrder.status
          })
          
          // delete vendor order if main order is cancelled
          if (isOrderCancelled) {
            await existingOrder.softDelete()
          }

          await existingOrder.useTransaction(trx).save()

          // Ensure all details are reassigned
          for (const detail of details) {
            if (detail.vendorOrderId !== existingOrder.id) {
              detail.vendorOrderId = existingOrder.id
              await detail.useTransaction(trx).save()
            }
          }
        }
      }

      await trx.commit()

      for (const vendorOrderId of newVendorOrderIds) {
        await this.vendorNotificationService.sendNewVendorOrderReceivedNotification(vendorOrderId);
      }
    } catch (err) {
      await trx.rollback()
      throw err
    }
  }
}
