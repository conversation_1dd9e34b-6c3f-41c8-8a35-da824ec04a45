import ZnVendor from "#models/zn_vendor";
import env from "#start/env";
import ZnVendorOrder from "#models/zn_vendor_order";
import VendorNotificationService from "./vendor_notification_service.js";
import { EApprovalStatus } from "#constants/approval_status";
import ZnOrderDetail from "#models/zn_order_detail";
import ZnProductVariant from "#models/zn_product_variant";
import { DateTime } from "luxon";
import db from "@adonisjs/lucid/services/db";
import { SMSService } from "#services/aws/sms_service";
import { AuthService } from "../../../services/auth/auth_service.js";
import { ShopifyAuthService } from "../../../services/shopify/auth/shopify_auth_service.js";
import logger from "@adonisjs/core/services/logger";
import ZnWarehouse from "#models/zn_warehouse";

export default class VendorService {
  private vendorNotificationService: VendorNotificationService;
  private smsService: SMSService;
  private authService: AuthService;
  private shopifyAuthService: ShopifyAuthService;

  constructor() {
    this.vendorNotificationService = new VendorNotificationService();
    this.smsService = new SMSService();
    this.authService = new AuthService();
    this.shopifyAuthService = new ShopifyAuthService();
  }

  async getSettings() {
    const defaultCommissionRate = env.get('VENDORS_SETTINGS_DEFAULT_COMMISSION_RATE');
    const defaultFixedCommissionAmount = env.get('VENDORS_SETTINGS_DEFAULT_FIXED_COMMISSION_AMOUNT');

    return {
      defaultCommissionRate: defaultCommissionRate ? parseFloat(defaultCommissionRate) : 0,
      defaultFixedCommissionAmount: defaultFixedCommissionAmount ? parseFloat(defaultFixedCommissionAmount) : 0
    };
  }

  async getAllVendors(page: number, limit: number, search: string | undefined, filter?: string[]) {
    const query = ZnVendor.query();

    if (search) {
      query.whereILike('companyName', `%${search}%`)
        .orWhereILike('brandName', `%${search}%`)
        .orWhereILike('website', `%${search}%`)
        .orWhereILike('contactName', `%${search}%`)
        .orWhereILike('phone', `%${search}%`)
        .orWhereILike('email', `%${search}%`);
    }

    if (filter) {
      query.where((queryBuilder) => {
        filter.map((fil: string) =>
          queryBuilder.orWhereRaw(`LOWER(${fil.split('=')[0]}) LIKE LOWER(?)`, [
            `%${fil.split('=')[1]}%`,
          ])
        )
      })
    }

    query.orderBy('companyName', 'asc');

    return await query.paginate(page, limit);
  }

  async getVendorById(vendorId: string) {
    return await ZnVendor.query()
      .where('id', vendorId)
      .preload('businessLicenseDocuments')
      .preload('users')
      .preload('warehouse')
      .firstOrFail();
  }

  async getVendorByUserId(userId: string) {
    return await ZnVendor.query()
      .whereHas('users', (query) => {
        query.where('id', userId);
      })
      .firstOrFail();
  }

  async getVendorStats(vendorId: string) {
    const vendor = await ZnVendor.query()
      .where('id', vendorId)
      .preload('vendorOrders')
      .preload('products')
      .preload('earnings')
      .preload('payments')
      .firstOrFail();

    const totalOrders = vendor.vendorOrders?.length ?? 0;
    const totalProducts = vendor.products?.length ?? 0;
    const totalSales = vendor.vendorOrders?.map(odr => odr.totalPrice).reduce((acc, amount) => acc + amount, 0) ?? 0;
    const totalEstimatedEarnings = vendor.earnings?.map((earning) => earning.earnedAmount).reduce((acc, amount) => acc + amount, 0) ?? 0;
    const totalApprovedEarnings = vendor.earnings
      .filter((earning) => earning.status === EApprovalStatus.APPROVED)
      .map((earning) => earning.earnedAmount)
      .reduce((acc, amount) => acc + amount, 0);
    const totalPayoutAmount = vendor.payments.map((payment) => payment.amount).reduce((acc, amount) => acc + amount, 0);

    return {
      totalOrders,
      totalSales,
      totalProducts,
      totalEstimatedEarnings,
      totalApprovedEarnings,
      totalPayoutAmount,
    };
  }

  async getVendorBalance(vendorId: string) {
    const vendor = await ZnVendor.query()
      .where('id', vendorId)
      .preload('earnings')
      .preload('payments')
      .firstOrFail();

    const totalApprovedEarnings = vendor.earnings
      .filter((earning) => earning.status === EApprovalStatus.APPROVED)
      .map((earning) => earning.earnedAmount)
      .reduce((acc, amount) => acc + amount, 0);
    const totalPayoutAmount = vendor.payments.map((payment) => payment.amount).reduce((acc, amount) => acc + amount, 0);

    const balance = totalApprovedEarnings - totalPayoutAmount

    if (balance > 0) {
      return balance;
    } else {
      logger.warn(`Balance of vendor ${vendor.id} is negative: ${balance}`);
      return 0;
    }
  }

  async createVendor(newVendor: any) {
    // Verify whether the vendor existed
    let existingVendor = await ZnVendor.query()
      .where('companyName', newVendor.companyName)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This company name is already registered'
      }
    }

    if (newVendor.ein) {
      existingVendor = await ZnVendor.query()
        .where('ein', newVendor.ein)
        .first();
      if (existingVendor) {
        return {
          success: false,
          messagge: 'This EIN is already used by an existing vendor'
        }
      }
    }

    existingVendor = await ZnVendor.query()
      .where('email', newVendor.email)
      .first();
    if (existingVendor) {
      return {
        success: false,
        messagge: 'This email is already used by an existing vendor'
      }
    }

    const settings = await this.getSettings();

    // Create vendor
    const createdVendor = await ZnVendor.create({
      companyName: newVendor.companyName,
      brandName: newVendor.brandName,
      website: newVendor.website,
      contactName: newVendor.contactName,
      phone: newVendor.phone,
      email: newVendor.email,
      address1: newVendor.address1,
      address2: newVendor.address2,
      city: newVendor.city,
      state: newVendor.state,
      country: newVendor.country,
      zipCode: newVendor.zipCode,
      ein: newVendor.ein,
      commissionRate: settings.defaultCommissionRate,
      fixedCommissionAmount: settings.defaultFixedCommissionAmount,
    });
    await createdVendor.related('businessLicenseDocuments').sync(newVendor.businessLicenseDocumentIds);

    // Create user
    await this.createUserForVendor(createdVendor.id, newVendor.contactName, newVendor.phone, newVendor.email);

    // Send notification
    await this.vendorNotificationService.sendRegistrationConfirmationNotification(createdVendor);

    return {
      success: true,
      vendor: createdVendor
    };
  }

  async update(vendorId: string, payload: any) {
    const updatingVendor = await ZnVendor.findOrFail(vendorId);

    const lastRegistrationStatus = updatingVendor.registrationStatus;

    const updatableFields = [
      'companyName',
      'brandName',
      'website',
      'contactName',
      'phone',
      'email',
      'address1',
      'address2',
      'city',
      'state',
      'country',
      'zipCode',
      'ein',
      'registrationStatus',
      'rejectionReason',
      'commissionRate',
      'fixedCommissionAmount'
    ];

    // assign warehouseId to vendor when vendor gets approved
    if (updatingVendor.registrationStatus == EApprovalStatus.PENDING
      && payload.registrationStatus == EApprovalStatus.APPROVED) {
      const vendorWarehouse = await ZnWarehouse.query()
        .whereNotNull('shopifyLocationId')
        .where({ isVendorPrimary: true })
        .first()

      if (vendorWarehouse) {
        updatingVendor.warehouseId = vendorWarehouse.id
      }
    }

    updatableFields.forEach(field => {
      if (Object.prototype.hasOwnProperty.call(payload, field)) {
        (updatingVendor as any)[field] = payload[field];
      }
    });
    if (lastRegistrationStatus === EApprovalStatus.REJECTED && updatingVendor.registrationStatus === EApprovalStatus.REJECTED) {
      updatingVendor.registrationStatus = EApprovalStatus.PENDING;
    }
    await updatingVendor.save();

    if (payload.businessLicenseDocumentIds) {
      await updatingVendor.related('businessLicenseDocuments').sync(payload.businessLicenseDocumentIds);
      await updatingVendor.load('businessLicenseDocuments');
    }

    // Create new user if necessary
    if (payload.email && payload.email !== updatingVendor.email) {
      await this.createUserForVendor(updatingVendor.id, payload.contactName, payload.phone, payload.email);
    }

    if (updatingVendor.registrationStatus !== lastRegistrationStatus) {
      if (updatingVendor.registrationStatus === EApprovalStatus.APPROVED) {
        await this.vendorNotificationService.sendRegistrationApprovedNotification(updatingVendor);
      } else if (updatingVendor.registrationStatus === EApprovalStatus.REJECTED) {
        await this.vendorNotificationService.sendRegistrationRejectedNotification(updatingVendor);
      }
    }

    return updatingVendor;
  }

  async delete(vendorId: string) {
    const vendor = await ZnVendor.find(vendorId);
    if (!vendor) return;
    await vendor.softDelete();
  }

  private async createUserForVendor(vendorId: string, contactName: string, phone: string, email: string) {
    const nameParts = this.splitContactName(contactName);
    const cleanedPhone = this.smsService.formatPhoneNumber(phone || '')

    const data = {
      firstName: nameParts.firstName,
      lastName: nameParts.lastName,
      email,
      phone: cleanedPhone,
    }

    const user = await this.authService.createUser(data)

    user.vendorId = vendorId;
    await user.save();

    // [Shopify] Search Customer with email
    const shopifyCustomer = await this.shopifyAuthService.getCustomerByEmail(email);

    if (!shopifyCustomer) {
      // [Shopify] Check if phone is taken
      const customerWithPhone = await this.shopifyAuthService.getCustomerByPhone(
        cleanedPhone || ''
      );
      // remove phone if phone exists on shopify
      if (customerWithPhone) {
        data.phone = null
      }

      // [Shopify] Create Customer
      await this.shopifyAuthService.createCustomer(data);
    }

    return user;
  }

  private splitContactName(contactName: string): { firstName: string; lastName: string; } {
    // Handle empty or whitespace-only strings
    if (!contactName || !contactName.trim()) {
      return { firstName: '', lastName: '' };
    }

    const trimmed = contactName.trim();
    const parts = trimmed.split(/\s+/); // Split on one or more whitespace characters

    if (parts.length === 1) {
      // Only one name provided
      return { firstName: parts[0], lastName: '' };
    }

    const firstName = parts[0];
    const lastName = parts.slice(1).join(' ');

    return { firstName, lastName };
  }

  async getVendorOrders(id: any, page = 10, limit: any) {
    //Filter order by product vendor
    return await ZnVendorOrder.query()
      .preload('order', (orderQuery) => {
        orderQuery.preload('user')
          .preload('shippingAddress')
          .whereHas('orderDetails', (orderDetailQuery) => {
            orderDetailQuery.whereHas('variant', (variantQuery) => {
              variantQuery.whereHas('product', (productQuery) => {
                productQuery.where('vendorId', id)
              })
            })
          })
      })
      .where("vendorId", id)
      .orderBy('createdAt', 'desc').paginate(page, limit);
  }

  async getOrderById(orderId: any) {
    return await ZnVendorOrder.findOrFail(orderId)
  }

  async getTopSellingProducts(vendorId: string) {
    const orderIds = await ZnVendorOrder.query()
      .where('vendorId', vendorId)
      .select('id')

    const orderIdList = orderIds.map((order) => order.id)

    if (orderIdList.length === 0) return []

    const topVariants = await ZnOrderDetail.query()
      .whereIn('vendorOrderId', orderIdList)
      .groupBy('variantId')
      .select('variantId')
      .sum('quantity as numberSold') // alias as numberSold
      .orderByRaw('SUM(quantity) DESC')
      .limit(10)

    const variantIds = topVariants
      .map((item) => item.variantId)
      .filter(variantId => variantId !== null);

    if (variantIds.length === 0) return []

    const soldMap = Object.fromEntries(
      topVariants.map((row) => [row.variantId, Number(row.$extras.numberSold)])
    )

    const variants = await ZnProductVariant.query()
      .whereIn('id', variantIds)
      .preload('image')
      .preload('product');

    variants.forEach((variant) => {
      variant.$extras.numberSold = soldMap[variant.id] ?? 0
    })

    // Sort by numberSold DESC
    variants.sort((a, b) => b.numberSold - a.numberSold)

    return variants
  }

  async getSalesStatistics(vendorId: string, days = 14) {
    const startDate = DateTime.now().minus({ days }).startOf('day')
    // Get daily grouped data
    const rows = await db
      .from('zn_order_details')
      .join('zn_vendor_orders', 'zn_vendor_orders.id', 'zn_order_details.vendorOrderId')
      .where('zn_vendor_orders.vendorId', vendorId)
      .andWhere('zn_order_details.createdAt', '>=', startDate.toSQLDate())
      .select(
        db.raw('DATE(zn_order_details.createdAt) as date'),
        db.raw('SUM(zn_order_details.price * zn_order_details.quantity) as totalSale'),
        db.raw('COUNT(DISTINCT zn_order_details.vendorOrderId) as totalOrders'),
        db.raw('SUM(zn_order_details.quantity) as numberSold')
      )
      .groupByRaw('DATE(zn_order_details.createdAt)')
      .orderBy('date')

    // Map by day
    const dataMap = Object.fromEntries(
      rows.map((row) => [
        DateTime.fromJSDate(row.date).toFormat('dd LLL'),
        {
          totalSale: Number(row.totalSale || 0),
          totalOrders: Number(row.totalOrders || 0),
          numberSold: Number(row.numberSold || 0),
        }
      ])
    )

    // Fill in missing days
    const labels: string[] = []
    const totalSale: number[] = []
    const totalOrders: number[] = []
    const numberSold: number[] = []

    for (let i = 0; i <= days; i++) {
      const day = startDate.plus({ days: i })
      const label = day.toFormat('dd LLL')
      const entry = dataMap[label] || { totalSale: 0, totalOrders: 0, numberSold: 0 }

      labels.push(label)
      totalSale.push(entry.totalSale)
      totalOrders.push(entry.totalOrders)
      numberSold.push(entry.numberSold)
    }

    // Final return
    return {
      labels,
      datasets: [
        { label: 'Total Sale', data: totalSale, type: "bar" },
        { label: 'Total Orders', data: totalOrders, type: "line" },
        { label: 'Number Sold', data: numberSold, type: "line" }
      ]
    }
  }

  async getMonthlySalesSummary(vendorId: string) {
    const startOfMonth = DateTime.now().startOf('month').toSQL()
    const endOfMonth = DateTime.now().endOf('month').toSQL()

    const vendor = await ZnVendor.query()
      .where('id', vendorId)
      .preload('products')
      .preload('vendorOrders', (query) => {
        query
          .whereBetween('createdAt', [startOfMonth, endOfMonth])
      })
      .preload('earnings', (query) => {
        query
          .whereBetween('createdAt', [startOfMonth, endOfMonth])
      })
      .firstOrFail()

    const totalOrders = vendor.vendorOrders?.length ?? 0
    const totalProducts = vendor.products?.length ?? 0

    const totalSales =
      vendor.vendorOrders
        ?.map((order) => Number(order.totalPrice))
        .reduce((acc, amount) => acc + amount, 0) ?? 0

    const totalEarnings =
      vendor.earnings
        ?.map((e) => Number(e.earnedAmount))
        .reduce((acc, amount) => acc + amount, 0) ?? 0

    const earningRate = totalSales > 0
      ? Number(((totalEarnings / totalSales) * 100).toFixed(2))
      : 0

    return {
      totalOrders,
      totalProducts,
      totalSales: parseFloat(totalSales.toFixed(2)),
      totalEarnings: parseFloat(totalEarnings.toFixed(2)),
      earningRate,
    }
  }

  async getMonthlyProfitSummary(vendorId: string) {
    const startOfMonth = DateTime.now().startOf('month').toSQL()
    const endOfMonth = DateTime.now().endOf('month').toSQL()

    // Get all order IDs for the vendor within the current month
    const orders = await ZnVendorOrder.query()
      .where('vendorId', vendorId)
      .whereBetween('createdAt', [startOfMonth, endOfMonth])
      .select('id')

    const orderIds = orders.map(o => o.id)

    if (orderIds.length === 0) {
      return {
        totalProfit: 0,
        profitGrowth: 0,
        categories: []
      }
    }


    const details = await ZnOrderDetail.query()
      .whereIn('vendorOrderId', orderIds)
      .preload('variant', (variantQuery) => {
        variantQuery.preload('product', (productQuery) => {
          productQuery.preload('category', (categoryQuery) => {
            categoryQuery.preload('image')
          })
        })
      })

    let totalProfit = 0
    const categoryMap: Record<string, { name: string; profit: number, image: any }> = {}

    for (const detail of details) {
      const product = detail.variant?.product
      const category = product?.category

      if (!product || !category) continue

      const lineProfit = detail.price * detail.quantity
      totalProfit += lineProfit

      if (!categoryMap[category.id]) {
        categoryMap[category.id] = {
          name: category.name,
          image: category.image,
          profit: 0
        }
      }

      categoryMap[category.id].profit += lineProfit
    }

    const categories = Object.values(categoryMap).map((entry) => ({
      name: entry.name,
      percentage: totalProfit > 0 ? +(entry.profit / totalProfit * 100).toFixed(2) : 0,
      icon: entry?.image?.url,
      color: this.getRandomColor()
    }))

    const lastMonthProfitRow = await db
      .from('zn_vendor_earnings')
      .where('vendorId', vendorId)
      .whereBetween('createdAt', [startOfMonth, endOfMonth])
      .sum('earnedAmount as total')

    const lastMonthProfit = Number(lastMonthProfitRow[0].total || 0)
    const profitGrowth = lastMonthProfit === 0 ? 100 : ((totalProfit - lastMonthProfit) / lastMonthProfit) * 100

    return {
      totalProfit: totalProfit.toFixed(2),
      profitGrowth: profitGrowth.toFixed(2),
      categories
    }
  }

  getRandomColor(): string {
    const colors = [
      '#8e54e9',
      '#f5bb74',
      '#00bcd4',
      '#4caf50',
      '#ff9800',
      '#e91e63',
      '#9c27b0',
      '#3f51b5',
      '#795548',
      '#607d8b',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}
