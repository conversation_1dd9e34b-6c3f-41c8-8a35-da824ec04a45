import { column, hasMany, hasManyThrough } from '@adonisjs/lucid/orm'
import AppModel from '#models/app_model'
import type { Has<PERSON>any, HasManyThrough } from '@adonisjs/lucid/types/relations'
import ZnInventory from '#models/zn_inventory'
import ZnInventoryLocation from './zn_inventory_location.js'

export default class ZnWarehouse extends AppModel {
  @column({ columnName: 'fulfilWarehouseId' })
  declare fulfilWarehouseId: number

  @column({ columnName: 'shopifyLocationId' })
  declare shopifyLocationId: string

  @column()
  declare name: string

  @column()
  declare code: string

  @column({ columnName: 'isNotification' })
  declare isNotification: boolean

  @column({
    columnName: 'isVendorPrimary',
    serialize: Boolean,
  })
  declare isVendorPrimary: boolean

  @hasMany(() => ZnInventoryLocation, {
    foreignKey: 'warehouseId',
  })
  declare locations: HasMany<typeof ZnInventoryLocation>

  @hasManyThrough([() => ZnInventory, () => ZnInventoryLocation], {
    foreignKey: 'warehouseId',
    throughForeignKey: 'locationId',
  })
  declare inventories: HasManyThrough<typeof ZnInventory>

  serializeExtras() {
    return {
      inventoriesCount: this.$extras.inventories_count,
    }
  }
}
