import AppModel from '#models/app_model'
import ZnProduct from '#models/zn_product'
import { belongsTo, column } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

export default class ZnProductImage extends AppModel {
  @column({ columnName: 'shopifyImageId' })
  declare shopifyImageId: string

  @column({ columnName: 'productId' })
  declare productId: string

  @column({ columnName: 'variantId' })
  declare variantId: string

  @column()
  declare src: string | null

  @column()
  declare position: number | null

  @column({ columnName: 'altText' })
  declare altText: string | null

  @column()
  declare width: number | null

  @column()
  declare height: number | null

  @belongsTo(() => ZnProduct)
  // @no-swagger
  declare product: BelongsTo<typeof ZnProduct>

  serializeExtras() {
    // add ?v=1 to image if not exists
    let newSrc = this.src
    if (this.src) {
      const urlParts = this.src.split('?')

      if (urlParts.length == 1) {
        newSrc = newSrc + '?v=1'
      }
    }

    return {
      src: newSrc
    }
  }
}
