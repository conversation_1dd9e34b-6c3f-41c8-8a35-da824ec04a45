import AppModel from '#models/app_model'
import ZnOrder from '#models/zn_order'
import { belongsTo, column, manyToMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, ManyToMany } from '@adonisjs/lucid/types/relations'
import ZnProductVariant from './zn_product_variant.js'
import ZnOrderFulfillment from './zn_order_fulfillment.js'
import ZnVendorOrder from './zn_vendor_order.js'

export default class ZnOrderDetail extends AppModel {
  @column({ columnName: 'orderId' })
  declare orderId: string

  @belongsTo(() => ZnOrder, {
    foreignKey: 'orderId',
  })
  declare order: BelongsTo<typeof ZnOrder>

  @column({ columnName: 'shopifyId' })
  declare shopifyId: string

  @column()
  declare title: string

  @column()
  declare quantity: number

  @column({ columnName: 'currentQuantity' })
  declare currentQuantity: number

  @column()
  declare sku: string

  @column({
    columnName: 'price',
    consume: (value: string) => parseFloat(value)
  })
  declare price: number

  @column({
    columnName: 'tax',
    consume: (value: string) => parseFloat(value)
  })
  declare tax: number

  @column({
    columnName: 'discount',
    consume: (value: string) => parseFloat(value)
  })
  declare discount: number

  @column({
    columnName: 'fulfillmentStatus',
    serialize: (value) => value || 'unfulfilled',
  })
  declare fulfillmentStatus: string

  @column({ columnName: 'variantId' })
  declare variantId: string | null

  @belongsTo(() => ZnProductVariant, {
    foreignKey: 'variantId',
  })
  declare public variant: BelongsTo<typeof ZnProductVariant>

  @manyToMany(() => ZnOrderFulfillment, {
    pivotTable: 'zn_order_fulfillments_and_order_details',
    pivotForeignKey: 'orderDetailId',
    pivotRelatedForeignKey: 'fulfillmentId',
    pivotColumns: ['quantity']
  })
  declare fulfillments: ManyToMany<typeof ZnOrderFulfillment>

  @column({ columnName: 'vendorOrderId' })
  declare vendorOrderId: string | null

  @belongsTo(() => ZnVendorOrder, {
    foreignKey: 'vendorOrderId'
  })
  declare vendorOrder: BelongsTo<typeof ZnVendorOrder>


  serializeExtras() {
    return {
      quantity: this.currentQuantity || this.quantity,
    }
  }
}
