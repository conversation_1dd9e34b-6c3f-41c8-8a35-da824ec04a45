import { belongsTo, column, manyToMany } from '@adonisjs/lucid/orm'
import AppModel from '../app_model.js'
import type { BelongsTo, ManyToMany } from '@adonisjs/lucid/types/relations'
import ZnStore from '#models/zn_store'
import ZnUser from '#models/zn_user'
import ZnStoreService from './zn_store_service.js'
import ZnStorePackage from './zn_store_package.js'
import ZnStoreTax from './zn_store_tax.js'
import { DateTime } from 'luxon'

export enum EAppointmentStatus {
  BOOKED = 'booked',
  CONFIRMED = 'confirmed',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  CHECKED_IN = 'checked-in',
  IN_SERVICE = 'in-service',
}

export default class ZnAppointment extends AppModel {
  static table = 'zn_appointments'

  @column({ columnName: 'storeId' })
  declare storeId: string

  @belongsTo(() => ZnStore, {
    foreignKey: 'storeId',
  })
  declare store: BelongsTo<typeof ZnStore>

  @column({ columnName: 'customerId' })
  declare customerId: string

  @belongsTo(() => ZnUser, {
    foreignKey: 'customerId',
  })
  declare customer: BelongsTo<typeof ZnUser>

  @column({ columnName: 'status' })
  declare status: EAppointmentStatus

  @column.dateTime({ columnName: 'startTime' })
  declare startTime: DateTime | null

  @column.dateTime({ columnName: 'endTime' })
  declare endTime: DateTime | null

  @column({ columnName: 'notes' })
  declare notes: string

  @column({ columnName: 'taxId' })
  declare taxId: string

  @belongsTo(() => ZnStoreTax, {
    foreignKey: 'taxId',
  })
  declare tax: BelongsTo<typeof ZnStoreTax>

  @column({ columnName: 'subtotal' })
  declare subtotal: number

  @column({ columnName: 'discount' })
  declare discount: number

  @column({ columnName: 'taxAmount' })
  declare taxAmount: number

  @column({ columnName: 'tipAmount' })
  declare tipAmount: number

  @column({ columnName: 'total' })
  declare total: number

  @manyToMany(() => ZnStoreService, {
    pivotTable: 'zn_appointment_services',
    pivotForeignKey: 'appointmentId',
    pivotRelatedForeignKey: 'serviceId',
    pivotColumns: ['price', 'duration'],
  })
  declare services: ManyToMany<typeof ZnStoreService>

  @manyToMany(() => ZnStorePackage, {
    pivotTable: 'zn_appointment_packages',
    pivotForeignKey: 'appointmentId',
    pivotRelatedForeignKey: 'packageId',
    pivotColumns: ['price'],
  })
  declare packages: ManyToMany<typeof ZnStorePackage>
}
